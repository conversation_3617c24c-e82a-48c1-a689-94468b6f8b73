# ZFMI Docker 优化部署指南

本文档介绍了优化后的 ZFMI Docker 部署方案，支持 mihomo 和 Python API 双服务运行。

## 🚀 主要特性

### 多服务架构
- **mihomo 核心服务**: 提供代理和网络功能
- **Python API 服务**: 提供配置管理和监控接口
- **进程监控服务**: 自动监控和重启异常服务

### 进程管理优化
- **Supervisor 模式**: 使用 supervisor 管理多个服务进程
- **传统模式**: 使用 shell 脚本管理进程（兼容模式）
- **自动重启**: 服务异常时自动重启，最多重试3次
- **优雅关闭**: 支持信号处理和优雅关闭

### 性能优化
- **多阶段构建**: 优化镜像大小和构建速度
- **预编译依赖**: Python 依赖预编译，减少启动时间
- **资源监控**: 内置健康检查和服务监控
- **日志管理**: 结构化日志和日志轮转

## 📦 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd zfmi

# 初始化环境
./deploy.sh init
```

### 2. 配置设置

编辑 `.env` 文件：

```bash
# 复制示例配置
cp .env.example .env

# 编辑配置文件
nano .env
```

重要配置项：
- `API_SECRET`: API 访问密钥（请修改为强密码）
- `MIHOMO_SECRET`: mihomo 控制器密钥
- `MIHOMO_VERSION`: mihomo 版本
- `SUPERVISOR_ENABLED`: 是否启用 supervisor 模式

### 3. 构建和启动

```bash
# 构建镜像
./deploy.sh build

# 启动服务
./deploy.sh start

# 查看状态
./deploy.sh status
```

## 🛠️ 部署脚本使用

### 基本命令

```bash
# 初始化环境
./deploy.sh init

# 构建镜像
./deploy.sh build

# 启动服务
./deploy.sh start

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 进入容器
./deploy.sh shell

# 更新服务
./deploy.sh update

# 备份配置
./deploy.sh backup

# 清理资源
./deploy.sh cleanup
```

### 高级用法

```bash
# 查看特定服务日志
./deploy.sh logs zfmi

# 开发模式启动
docker-compose --profile dev up -d zfmi-dev

# 手动构建指定版本
MIHOMO_VERSION=v1.19.10 ./deploy.sh build
```

## 🔧 Docker Compose 配置

### 服务配置

主要服务 `zfmi`:
- 使用 host 网络模式
- 支持 TUN 模式（需要 NET_ADMIN 权限）
- 自动重启策略
- 健康检查机制

开发服务 `zfmi-dev`:
- 启用调试模式
- 关闭 supervisor 模式
- 支持热重载

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SUPERVISOR_ENABLED` | `true` | 是否启用 supervisor |
| `API_SECRET` | `jhxnb666` | API 访问密钥 |
| `MIHOMO_SECRET` | `jhxnb666` | mihomo 密钥 |
| `LOG_LEVEL` | `info` | 日志级别 |
| `MONITOR_INTERVAL` | `30` | 监控检查间隔（秒） |
| `MAX_RESTART_ATTEMPTS` | `3` | 最大重启次数 |

### 卷挂载

- `./config.yaml`: mihomo 配置文件
- `mihomo_data`: 备份数据持久化
- `mihomo_logs`: 日志文件持久化
- `supervisor_logs`: supervisor 日志

## 🔍 监控和调试

### 健康检查

容器内置健康检查：
```bash
# 手动执行健康检查
docker exec zfmi /entrypoint.sh healthcheck
```

### 服务监控

监控脚本自动运行，也可手动执行：
```bash
# 进入容器
docker exec -it zfmi /bin/sh

# 执行监控检查
/monitor.sh check
```

### 日志查看

```bash
# 查看所有日志
docker-compose logs -f

# 查看 mihomo 日志
docker exec zfmi tail -f /var/log/mihomo/mihomo.log

# 查看 API 日志
docker exec zfmi tail -f /var/log/mihomo/api.log

# 查看监控日志
docker exec zfmi tail -f /var/log/mihomo/monitor.log
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细日志
   ./deploy.sh logs
   
   # 检查配置文件
   docker exec zfmi cat /etc/mihomo/config.yaml
   ```

2. **权限问题**
   ```bash
   # 确保容器有足够权限
   docker run --privileged --cap-add=NET_ADMIN ...
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep -E '(10801|11024|8888)'
   ```

### 调试模式

启用调试模式：
```bash
# 设置环境变量
export DEBUG=true
export LOG_LEVEL=debug

# 重启服务
./deploy.sh restart
```

## 📊 性能优化

### 资源配置

根据需要调整资源限制：
```yaml
# docker-compose.yml
services:
  zfmi:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
```

### 网络优化

- 使用 host 网络模式获得最佳性能
- TUN 模式需要 NET_ADMIN 权限
- 考虑使用 macvlan 网络（高级用法）

## 🔐 安全建议

1. **修改默认密钥**
   - 更改 `API_SECRET` 和 `MIHOMO_SECRET`
   - 使用强密码（至少16位）

2. **网络安全**
   - 限制 CORS 源
   - 使用防火墙规则
   - 考虑使用 HTTPS

3. **容器安全**
   - 定期更新镜像
   - 使用非 root 用户（如果可能）
   - 限制容器权限

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 多服务架构优化
- ✅ Supervisor 进程管理
- ✅ 自动监控和重启
- ✅ 配置系统重构
- ✅ 部署脚本优化
- ✅ 健康检查增强

### v1.0.0
- ✅ 基础 Docker 部署
- ✅ mihomo + Python API
- ✅ 基本健康检查
