#!/bin/bash

# 测试监控脚本修复效果

set -e

CONTAINER_NAME="zfmi-monitor-test"
IMAGE_NAME="jhxxr/mihomo:latest"

echo "🔧 测试监控脚本修复效果"
echo "========================"

# 清理可能存在的测试容器
echo "🧹 清理旧的测试容器..."
docker rm -f $CONTAINER_NAME 2>/dev/null || true

# 启动测试容器
echo "🚀 启动测试容器..."
docker run -d \
  --name $CONTAINER_NAME \
  --network host \
  --cap-add NET_ADMIN \
  -e MIHOMO_LOG_LEVEL=info \
  -e SUPERVISOR_ENABLED=true \
  -e MONITOR_INTERVAL=10 \
  $IMAGE_NAME

echo "⏳ 等待容器启动..."
sleep 15

echo "📋 检查容器状态..."
docker ps | grep $CONTAINER_NAME || echo "容器未运行"

echo ""
echo "🔍 检查 supervisor 服务状态:"
echo "============================"
docker exec $CONTAINER_NAME supervisorctl -s unix:///var/run/supervisor.sock status

echo ""
echo "📝 显示最近的监控日志:"
echo "======================"
docker logs $CONTAINER_NAME 2>&1 | grep "\[MONITOR\]" | tail -10

echo ""
echo "🧪 测试监控脚本的 supervisorctl 命令:"
echo "===================================="
docker exec $CONTAINER_NAME supervisorctl -s unix:///var/run/supervisor.sock status mihomo

echo ""
echo "✅ 修复验证完成！"
echo ""
echo "🔄 实时监控日志 (按 Ctrl+C 停止):"
echo "================================"
docker logs -f $CONTAINER_NAME 2>&1 | grep --line-buffered "\[MONITOR\]"

# 清理函数
cleanup() {
    echo ""
    echo "🧹 清理测试容器..."
    docker rm -f $CONTAINER_NAME 2>/dev/null || true
    echo "✅ 清理完成"
}

# 设置清理陷阱
trap cleanup EXIT INT TERM
