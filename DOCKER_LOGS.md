# Docker 日志配置说明

## 概述

现在 mihomo Docker 容器的所有服务日志都会输出到 `docker logs`，方便统一查看和管理。

## 配置改动

### 1. Supervisor 配置 (`supervisord.conf`)

**主要改动**:
- 将所有服务的日志输出重定向到 `/dev/stdout` 和 `/dev/stderr`
- 设置 `stdout_logfile_maxbytes=0` 禁用日志轮转
- supervisor 自身的日志也输出到 stdout

**具体配置**:
```ini
[supervisord]
logfile=/dev/stdout
logfile_maxbytes=0

[program:mihomo]
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:api]
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:monitor]
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
```

### 2. Python API 日志配置 (`api/main.py`)

**改动**:
- 添加 `[API]` 前缀标识
- 强制输出到 stdout
- 使用 `force=True` 覆盖现有配置

```python
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL.upper()),
    format='[API] %(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout,
    force=True
)
```

### 3. 监控脚本日志配置 (`monitor.sh`)

**改动**:
- 移除文件日志输出
- 添加 `[MONITOR]` 前缀标识
- 直接输出到 stdout

```bash
log_monitor() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[MONITOR] $timestamp [$level] $message"
}
```

## 使用方法

### 查看实时日志
```bash
# 查看所有日志
docker logs -f zfmi

# 查看最近的日志
docker logs --tail 100 zfmi

# 查看特定时间段的日志
docker logs --since "2024-01-01T00:00:00" zfmi
```

### 过滤特定服务的日志
```bash
# 只看 mihomo 日志
docker logs zfmi 2>&1 | grep -v "\[API\]" | grep -v "\[MONITOR\]"

# 只看 API 日志
docker logs zfmi 2>&1 | grep "\[API\]"

# 只看监控日志
docker logs zfmi 2>&1 | grep "\[MONITOR\]"

# 只看 supervisor 日志
docker logs zfmi 2>&1 | grep "supervisord"
```

### 导出日志到文件
```bash
# 导出所有日志
docker logs zfmi > mihomo-logs.txt 2>&1

# 导出最近1000行日志
docker logs --tail 1000 zfmi > mihomo-recent.txt 2>&1

# 导出特定时间段日志
docker logs --since "1h" zfmi > mihomo-last-hour.txt 2>&1
```

## 日志格式说明

### 1. Supervisor 日志
```
2024-01-01 12:00:00,123 INFO supervisord started with pid 1
2024-01-01 12:00:01,456 INFO spawned: 'mihomo' with pid 8
2024-01-01 12:00:01,789 INFO spawned: 'api' with pid 9
2024-01-01 12:00:02,012 INFO spawned: 'monitor' with pid 10
```

### 2. mihomo 日志
```
time="2024-01-01T12:00:00+08:00" level=info msg="Start initial configuration in progress"
time="2024-01-01T12:00:00+08:00" level=info msg="HTTP proxy listening at: 127.0.0.1:10801"
time="2024-01-01T12:00:00+08:00" level=info msg="RESTful API listening at: 127.0.0.1:11024"
```

### 3. API 日志
```
[API] 2024-01-01 12:00:00,123 - uvicorn.error - INFO - Started server process [9]
[API] 2024-01-01 12:00:00,456 - uvicorn.error - INFO - Waiting for application startup.
[API] 2024-01-01 12:00:00,789 - uvicorn.error - INFO - Application startup complete.
```

### 4. 监控日志
```
[MONITOR] 2024-01-01 12:00:30 [INFO] 开始监控服务健康状态...
[MONITOR] 2024-01-01 12:01:00 [SUCCESS] mihomo 服务健康检查通过
[MONITOR] 2024-01-01 12:01:00 [SUCCESS] API 服务健康检查通过
```

## 测试脚本

使用提供的测试脚本验证日志输出：

```bash
# 给脚本执行权限
chmod +x test-logs.sh

# 运行测试
./test-logs.sh
```

测试脚本会：
1. 启动一个测试容器
2. 检查各服务的日志输出
3. 显示实时日志
4. 自动清理测试容器

## 故障排除

### 1. 日志不显示
- 检查容器是否正常运行：`docker ps`
- 检查容器状态：`docker inspect zfmi`
- 重启容器：`docker restart zfmi`

### 2. 日志格式异常
- 检查 supervisor 配置：`docker exec zfmi cat /etc/supervisor/conf.d/supervisord.conf`
- 检查服务状态：`docker exec zfmi supervisorctl status`

### 3. 特定服务日志缺失
```bash
# 检查服务状态
docker exec zfmi supervisorctl status

# 重启特定服务
docker exec zfmi supervisorctl restart mihomo
docker exec zfmi supervisorctl restart api
docker exec zfmi supervisorctl restart monitor
```

## 日志管理建议

### 1. 日志轮转
Docker 自身提供日志轮转功能，建议配置：

```bash
# 在 docker run 时添加日志配置
docker run -d \
  --name zfmi \
  --log-driver json-file \
  --log-opt max-size=100m \
  --log-opt max-file=3 \
  --network host \
  --cap-add NET_ADMIN \
  jhxxr/mihomo:latest
```

### 2. 日志收集
对于生产环境，建议使用日志收集系统：
- ELK Stack (Elasticsearch + Logstash + Kibana)
- Fluentd
- Promtail + Loki + Grafana

### 3. 监控告警
基于日志内容设置监控告警：
- 错误日志告警
- 服务重启告警
- 性能指标监控

## 注意事项

1. **性能影响**: 所有日志输出到 stdout 可能会增加 I/O 开销
2. **存储空间**: 确保 Docker 主机有足够的磁盘空间存储日志
3. **日志级别**: 可通过环境变量 `MIHOMO_LOG_LEVEL` 调整日志详细程度
4. **网络日志**: mihomo 的网络连接日志可能会很多，注意磁盘空间

## 环境变量

相关的环境变量配置：

```bash
# mihomo 日志级别
MIHOMO_LOG_LEVEL=info

# API 调试模式
DEBUG=false

# 监控间隔
MONITOR_INTERVAL=30

# Supervisor 模式
SUPERVISOR_ENABLED=true
```

现在你可以使用 `docker logs -f zfmi` 来查看所有服务的实时日志输出了！
