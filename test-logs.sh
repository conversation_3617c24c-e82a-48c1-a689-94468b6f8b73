#!/bin/bash

# 测试Docker日志输出脚本

set -e

CONTAINER_NAME="zfmi-test"
IMAGE_NAME="jhxxr/mihomo:latest"

echo "🧪 测试 mihomo Docker 容器日志输出"
echo "=================================="

# 清理可能存在的测试容器
echo "🧹 清理旧的测试容器..."
docker rm -f $CONTAINER_NAME 2>/dev/null || true

# 启动测试容器
echo "🚀 启动测试容器..."
docker run -d \
  --name $CONTAINER_NAME \
  --network host \
  --cap-add NET_ADMIN \
  -e MIHOMO_LOG_LEVEL=info \
  -e SUPERVISOR_ENABLED=true \
  $IMAGE_NAME

echo "⏳ 等待容器启动..."
sleep 10

echo "📋 显示容器状态..."
docker ps | grep $CONTAINER_NAME || echo "容器未运行"

echo ""
echo "📝 显示容器日志 (前50行):"
echo "=========================="
docker logs $CONTAINER_NAME 2>&1 | head -50

echo ""
echo "🔍 检查日志中的关键信息:"
echo "=========================="

# 检查supervisor日志
if docker logs $CONTAINER_NAME 2>&1 | grep -q "supervisord started"; then
    echo "✅ Supervisor 启动成功"
else
    echo "❌ Supervisor 启动失败"
fi

# 检查mihomo日志
if docker logs $CONTAINER_NAME 2>&1 | grep -q "mihomo"; then
    echo "✅ mihomo 日志输出正常"
else
    echo "❌ mihomo 日志未找到"
fi

# 检查API日志
if docker logs $CONTAINER_NAME 2>&1 | grep -q "\[API\]"; then
    echo "✅ API 日志输出正常"
else
    echo "❌ API 日志未找到"
fi

# 检查监控日志
if docker logs $CONTAINER_NAME 2>&1 | grep -q "\[MONITOR\]"; then
    echo "✅ 监控日志输出正常"
else
    echo "❌ 监控日志未找到"
fi

echo ""
echo "🔄 实时日志输出 (按 Ctrl+C 停止):"
echo "=================================="
echo "运行命令: docker logs -f $CONTAINER_NAME"
echo ""

# 实时显示日志
docker logs -f $CONTAINER_NAME

# 清理函数
cleanup() {
    echo ""
    echo "🧹 清理测试容器..."
    docker rm -f $CONTAINER_NAME 2>/dev/null || true
    echo "✅ 清理完成"
}

# 设置清理陷阱
trap cleanup EXIT INT TERM
