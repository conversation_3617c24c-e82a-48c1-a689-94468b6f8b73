# ZFMI Docker 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# ==================== 基础配置 ====================
# mihomo版本
MIHOMO_VERSION=v1.19.11

# 时区设置
TZ=Asia/Shanghai

# 数据目录（用于持久化存储）
DATA_DIR=./data

# 自定义配置目录
CUSTOM_CONFIG_DIR=./custom

# ==================== 日志配置 ====================
# 日志级别: debug, info, warning, error
LOG_LEVEL=info
MIHOMO_LOG_LEVEL=info

# ==================== 安全配置 ====================
# API访问密钥（请修改为强密码）
API_SECRET=jhxnb666

# mihomo控制器密钥（请修改为强密码）
MIHOMO_SECRET=jhxnb666

# ==================== 服务配置 ====================
# 是否启用Supervisor进程管理
SUPERVISOR_ENABLED=true

# API服务配置
API_HOST=0.0.0.0
API_PORT=8888
API_WORKERS=1

# ==================== 性能配置 ====================
# API连接保持时间（秒）
API_KEEPALIVE_TIMEOUT=30

# mihomo API超时时间（秒）
MIHOMO_API_TIMEOUT=10

# ==================== 监控配置 ====================
# 服务监控检查间隔（秒）
MONITOR_INTERVAL=30

# 最大重启尝试次数
MAX_RESTART_ATTEMPTS=3

# ==================== CORS配置 ====================
# 允许的跨域源，多个用逗号分隔
CORS_ORIGINS=*

# 是否允许携带凭证
CORS_ALLOW_CREDENTIALS=true

# ==================== 开发配置 ====================
# 是否启用调试模式
DEBUG=false

# 是否启用热重载（仅开发环境）
RELOAD=false

# ==================== 高级配置 ====================
# 最大备份文件数量
MAX_BACKUP_FILES=10

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# ==================== 网络配置 ====================
# 如果不使用host网络模式，可以配置以下端口映射
# MIXED_PORT=10801
# CONTROLLER_PORT=11024
# TUN_PORT=12790
# API_SERVER_PORT=8888

# ==================== Docker配置 ====================
# Docker镜像标签
IMAGE_TAG=latest

# 容器名称
CONTAINER_NAME=zfmi

# 重启策略: no, always, unless-stopped, on-failure
RESTART_POLICY=unless-stopped
