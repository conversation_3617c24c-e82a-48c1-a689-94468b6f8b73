#!/usr/bin/env python3
"""
API服务配置管理
"""

import os
from typing import Optional
from pathlib import Path

class APIConfig:
    """API配置类"""
    
    # 基础配置
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8888"))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "info")
    
    # 安全配置
    API_SECRET: str = os.getenv("API_SECRET", "jhxnb666")
    MIHOMO_SECRET: str = os.getenv("MIHOMO_SECRET", "jhxnb666")
    
    # 文件路径配置
    CONFIG_FILE: str = os.getenv("MIHOMO_CONFIG_FILE", "/etc/mihomo/config.yaml")
    BACKUP_DIR: str = os.getenv("MIHOMO_BACKUP_DIR", "/etc/mihomo/backups")
    LOG_DIR: str = os.getenv("LOG_DIR", "/var/log/mihomo")
    
    # mihomo API配置
    MIHOMO_API_BASE: str = os.getenv("MIHOMO_API_BASE", "http://localhost:11024")
    MIHOMO_API_TIMEOUT: int = int(os.getenv("MIHOMO_API_TIMEOUT", "10"))
    
    # 性能配置
    WORKERS: int = int(os.getenv("API_WORKERS", "1"))
    WORKER_CONNECTIONS: int = int(os.getenv("API_WORKER_CONNECTIONS", "1000"))
    KEEPALIVE_TIMEOUT: int = int(os.getenv("API_KEEPALIVE_TIMEOUT", "30"))
    GRACEFUL_TIMEOUT: int = int(os.getenv("API_GRACEFUL_TIMEOUT", "10"))
    
    # CORS配置
    CORS_ORIGINS: list = os.getenv("CORS_ORIGINS", "*").split(",")
    CORS_ALLOW_CREDENTIALS: bool = os.getenv("CORS_ALLOW_CREDENTIALS", "false").lower() == "true"
    CORS_ALLOW_METHODS: list = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"]
    CORS_ALLOW_HEADERS: list = ["*"]
    CORS_EXPOSE_HEADERS: list = ["*"]
    
    # 监控配置
    HEALTH_CHECK_INTERVAL: int = int(os.getenv("HEALTH_CHECK_INTERVAL", "30"))
    MAX_BACKUP_FILES: int = int(os.getenv("MAX_BACKUP_FILES", "10"))
    
    # 开发模式
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    RELOAD: bool = os.getenv("RELOAD", "false").lower() == "true"
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        directories = [
            cls.BACKUP_DIR,
            cls.LOG_DIR,
            os.path.dirname(cls.CONFIG_FILE)
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_uvicorn_config(cls) -> dict:
        """获取uvicorn配置"""
        return {
            "host": cls.API_HOST,
            "port": cls.API_PORT,
            "reload": cls.RELOAD,
            "log_level": cls.LOG_LEVEL,
            "workers": cls.WORKERS,
            "access_log": True,
            "use_colors": False,
            "server_header": False,
            "date_header": False,
            "loop": "asyncio",
            "http": "h11",
            "timeout_keep_alive": cls.KEEPALIVE_TIMEOUT,
            "timeout_graceful_shutdown": cls.GRACEFUL_TIMEOUT,
        }
    
    @classmethod
    def get_cors_config(cls) -> dict:
        """获取CORS配置"""
        return {
            "allow_origins": cls.CORS_ORIGINS,
            "allow_credentials": cls.CORS_ALLOW_CREDENTIALS,
            "allow_methods": cls.CORS_ALLOW_METHODS,
            "allow_headers": cls.CORS_ALLOW_HEADERS,
            "expose_headers": cls.CORS_EXPOSE_HEADERS,
            "allow_origin_regex": None,
            "max_age": 600,
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置"""
        errors = []
        
        # 检查必要的文件路径
        config_dir = os.path.dirname(cls.CONFIG_FILE)
        if not os.path.exists(config_dir):
            errors.append(f"配置目录不存在: {config_dir}")
        
        # 检查端口范围
        if not (1 <= cls.API_PORT <= 65535):
            errors.append(f"API端口超出范围: {cls.API_PORT}")
        
        # 检查密钥长度
        if len(cls.API_SECRET) < 6:
            errors.append("API密钥长度不足6位")
        
        if errors:
            for error in errors:
                print(f"配置错误: {error}")
            return False
        
        return True
    
    @classmethod
    def print_config(cls):
        """打印当前配置（隐藏敏感信息）"""
        print("=== API服务配置 ===")
        print(f"监听地址: {cls.API_HOST}:{cls.API_PORT}")
        print(f"日志级别: {cls.LOG_LEVEL}")
        print(f"工作进程: {cls.WORKERS}")
        print(f"调试模式: {cls.DEBUG}")
        print(f"热重载: {cls.RELOAD}")
        print(f"配置文件: {cls.CONFIG_FILE}")
        print(f"备份目录: {cls.BACKUP_DIR}")
        print(f"日志目录: {cls.LOG_DIR}")
        print(f"mihomo API: {cls.MIHOMO_API_BASE}")
        print(f"CORS源: {cls.CORS_ORIGINS}")
        print("==================")

# 创建全局配置实例
config = APIConfig()

# 在导入时验证配置
if not config.validate_config():
    raise RuntimeError("配置验证失败")

# 确保目录存在
config.ensure_directories()
