# 多架构 mihomo + Python API Docker 镜像
# 支持 AMD64 和 ARM64 架构，优化多服务运行

# 第一阶段：下载 mihomo 二进制文件
FROM alpine:3.19 AS downloader

# 安装必要工具
RUN apk add --no-cache curl ca-certificates

# 设置 mihomo 版本
ARG MIHOMO_VERSION=v1.19.11

# 设置架构变量
ARG TARGETARCH
ARG TARGETOS

# 根据架构设置下载文件名
RUN case "${TARGETARCH}" in \
    amd64) ARCH_SUFFIX="linux-amd64-compatible-go123" ;; \
    arm64) ARCH_SUFFIX="linux-arm64" ;; \
    armv7) ARCH_SUFFIX="linux-armv7" ;; \
    386) ARCH_SUFFIX="linux-386" ;; \
    *) echo "Unsupported architecture: ${TARGETARCH}" && exit 1 ;; \
    esac && \
    echo "Downloading mihomo for ${ARCH_SUFFIX}" && \
    curl -L "https://github.com/MetaCubeX/mihomo/releases/download/${MIHOMO_VERSION}/mihomo-${ARCH_SUFFIX}-${MIHOMO_VERSION}.gz" | \
    gunzip -c > /tmp/mihomo && \
    chmod +x /tmp/mihomo

# 第二阶段：Python依赖构建
FROM python:3.11-alpine AS python-builder

# 安装构建依赖
RUN apk add --no-cache \
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev \
    cargo \
    rust

# 复制requirements文件
COPY api/requirements.txt /tmp/requirements.txt

# 预编译Python依赖
RUN pip install --no-cache-dir --user -r /tmp/requirements.txt

# 第三阶段：构建最终镜像
FROM python:3.11-alpine

# 安装运行时依赖（仅运行时需要的包）
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    iptables \
    ip6tables \
    iproute2 \
    curl \
    supervisor \
    && rm -rf /var/cache/apk/*

# 创建 mihomo 用户和组
RUN addgroup -g 1000 mihomo && \
    adduser -u 1000 -G mihomo -s /bin/sh -D mihomo

# 创建必要目录
RUN mkdir -p \
    /etc/mihomo \
    /var/log/mihomo \
    /etc/mihomo/backups \
    /var/log/supervisor \
    /etc/supervisor/conf.d \
    && chown -R mihomo:mihomo /etc/mihomo /var/log/mihomo

# 从下载阶段复制 mihomo 二进制文件
COPY --from=downloader /tmp/mihomo /usr/local/bin/mihomo

# 从Python构建阶段复制预编译的依赖
COPY --from=python-builder /root/.local /root/.local
ENV PATH=/root/.local/bin:$PATH

# 复制配置文件
COPY config.yaml /etc/mihomo/config.yaml

# 复制 Python API 代码
COPY api/ /app/
WORKDIR /app

# 复制启动脚本、监控脚本和supervisor配置
COPY entrypoint.sh /entrypoint.sh
COPY monitor.sh /monitor.sh
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
RUN chmod +x /entrypoint.sh /monitor.sh

# 设置工作目录
WORKDIR /etc/mihomo

# 暴露端口（仅用于文档说明，实际使用主机网络）
EXPOSE 10801 11024 12790 8888

# 设置环境变量
ENV MIHOMO_CONFIG_DIR=/etc/mihomo \
    MIHOMO_LOG_LEVEL=info \
    PYTHONPATH=/app \
    API_SECRET=jhxnb666 \
    MIHOMO_SECRET=jhxnb666 \
    PYTHONUNBUFFERED=1 \
    SUPERVISOR_ENABLED=true

# 使用非特权用户运行（但需要 NET_ADMIN 权限用于 TUN）
USER root

# 健康检查 - 改进版本，检查两个服务
HEALTHCHECK --interval=30s --timeout=15s --start-period=30s --retries=3 \
    CMD /entrypoint.sh healthcheck

# 启动命令 - 支持supervisor和传统模式
ENTRYPOINT ["/entrypoint.sh"]
CMD ["start"]
