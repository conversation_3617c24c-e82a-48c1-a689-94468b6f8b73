# zfmi 转发

一个包含 mihomo 和预配置的 Docker 镜像，支持 AMD64 和 ARM64 架构，使用主机网络模式，一步到位运行代理服务。

## 特性

- ✅ **多架构支持**: 支持 AMD64 和 ARM64 架构
- ✅ **预配置**: 内置配置文件，开箱即用
- ✅ **主机网络**: 使用主机网络模式，无需端口映射
- ✅ **自动构建**: GitHub Actions 自动构建和发布
- ✅ **安全扫描**: 集成 Trivy 安全扫描
- ✅ **健康检查**: 内置健康检查机制
- ✅ **优雅关闭**: 支持信号处理和优雅关闭

## 快速开始

### 使用 Docker 运行

```bash
# 使用主机网络模式运行（推荐）
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  jhxxr/zfmi:latest

# 查看日志
docker logs -f zfmi

# 停止容器
docker stop zfmi
```

### 使用 Docker Compose

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  mihomo:
    image: jhxxr/zfmi:latest
    container_name: zfmi
    network_mode: host
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
    environment:
      - MIHOMO_LOG_LEVEL=info
      - TZ=Asia/Shanghai
      - API_SECRET=your-api-secret
      - MIHOMO_SECRET=your-mihomo-secret
      - SUPERVISOR_ENABLED=true
    volumes:
      - ./logs:/var/log/mihomo  # 可选：持久化日志
```

运行：

```bash
docker-compose up -d
```

## 端口说明

由于使用主机网络模式，以下端口将直接在主机上监听：

- `10801`: Mixed Port (HTTP/SOCKS5 代理端口)
- `11024`: External Controller (RESTful API 端口)
- `12790`: 隧道转发端口

## 配置说明

### 默认配置

镜像内置了以下配置：

- **Mixed Port**: 10801 (HTTP/SOCKS5 代理)
- **External Controller**: 0.0.0.0:11024
- **API 密钥**: `jhxnb666`
- **TUN 模式**: 启用
- **日志级别**: info

### 自定义配置

如果需要自定义配置，可以挂载配置文件：

```bash
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  -v /root/zfmi/config.yaml:/etc/mihomo/config.yaml:ro \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  jhxxr/zfmi:latest
```

### 更新

```bash
# 删除未使用的镜像
docker rmi $(docker images -f "dangling=true" -q)
docker pull jhxxr/zfmi:latest
docker stop zfmi
docker rm zfmi
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  jhxxr/zfmi:latest
```

## 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MIHOMO_LOG_LEVEL` | `info` | 日志级别 (debug, info, warning, error) |
| `MIHOMO_CONFIG_DIR` | `/etc/mihomo` | 配置文件目录 |

## 构建说明

### 本地构建

```bash
# 克隆仓库
git clone https://github.com/your-username/mihomo.git
cd mihomo

# 构建镜像
./build.sh --name mihomo --tag latest

# 构建并推送到 Docker Hub
./build.sh --name mihomo --tag latest --registry your-username/ --push
```

### GitHub Actions 自动构建

项目配置了 GitHub Actions 自动构建：

1. **推送到主分支**: 自动构建并推送 `latest` 标签
2. **创建 Release**: 自动构建并推送版本标签
3. **手动触发**: 可以手动触发构建指定版本

#### 设置 Secrets

在 GitHub 仓库设置中添加以下 Secrets：

- `DOCKER_USERNAME`: Docker Hub 用户名
- `DOCKER_PASSWORD`: Docker Hub 密码或访问令牌

## 使用示例

### 基本代理使用

```bash
# 设置 HTTP 代理
export http_proxy=http://localhost:10801
export https_proxy=http://localhost:10801

# 设置 SOCKS5 代理
export ALL_PROXY=socks5://localhost:10801

# 测试连接
curl -I https://www.google.com
```

### API 管理

```bash
# 获取版本信息
curl -H "Authorization: Bearer jhxnb666" http://localhost:11024/version

# 获取代理信息
curl -H "Authorization: Bearer jhxnb666" http://localhost:11024/proxies

# 切换代理
curl -X PUT -H "Authorization: Bearer jhxnb666" \
  -H "Content-Type: application/json" \
  -d '{"name":"🇭🇰 [直连]香港anytls[xxc][新协议]"}' \
  http://localhost:11024/proxies/ALL-PROXY
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker logs mihomo
   
   # 检查配置文件
   docker exec mihomo cat /etc/mihomo/config.yaml
   ```

2. **网络连接问题**
   ```bash
   # 检查端口监听
   netstat -tlnp | grep -E "(10801|11024|12790)"
   
   # 测试 API 连接
   curl http://localhost:11024/version
   ```

3. **权限问题**
   - 确保容器有 `NET_ADMIN` 权限
   - 检查 TUN 设备是否可用

### 调试模式

```bash
# 启用调试日志
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  -e MIHOMO_LOG_LEVEL=debug \
  your-username/mihomo:latest
```

## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 相关链接

- [mihomo 官方仓库](https://github.com/MetaCubeX/mihomo)
- [Docker Hub](https://hub.docker.com/r/your-username/mihomo)
- [GitHub Actions](https://github.com/your-username/mihomo/actions)
