#!/bin/sh

# mihomo Docker 容器启动脚本
# 支持优雅关闭、信号处理和多种运行模式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SUPERVISOR_ENABLED=${SUPERVISOR_ENABLED:-true}
MIHOMO_PID=""
API_PID=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 信号处理函数
cleanup() {
    log_info "接收到终止信号，正在优雅关闭服务..."

    if [ "$SUPERVISOR_ENABLED" = "true" ]; then
        # Supervisor模式
        if pgrep supervisord > /dev/null; then
            log_info "停止 supervisor..."
            supervisorctl shutdown || true
            sleep 2
        fi
    else
        # 传统模式
        if [ -n "$MIHOMO_PID" ]; then
            log_info "停止 mihomo..."
            kill -TERM "$MIHOMO_PID" 2>/dev/null || true
            wait "$MIHOMO_PID" 2>/dev/null || true
        fi

        if [ -n "$API_PID" ]; then
            log_info "停止 API 服务器..."
            kill -TERM "$API_PID" 2>/dev/null || true
            wait "$API_PID" 2>/dev/null || true
        fi
    fi

    log_success "所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGTERM SIGINT SIGQUIT

# 检查配置文件
check_config() {
    if [ ! -f "/etc/mihomo/config.yaml" ]; then
        log_error "配置文件 /etc/mihomo/config.yaml 不存在"
        exit 1
    fi
    
    log_success "配置文件检查通过"
}

# 检查 mihomo 二进制文件
check_binary() {
    if [ ! -f "/usr/local/bin/mihomo" ]; then
        log_error "mihomo 二进制文件不存在"
        exit 1
    fi
    
    if [ ! -x "/usr/local/bin/mihomo" ]; then
        log_error "mihomo 二进制文件没有执行权限"
        exit 1
    fi
    
    log_success "mihomo 二进制文件检查通过"
}

# 显示版本信息
show_version() {
    log_info "mihomo 版本信息："
    /usr/local/bin/mihomo -v
}

# 显示网络信息
show_network_info() {
    log_info "网络配置信息："
    echo "主机名: $(hostname)"
    echo "网络接口:"
    ip addr show | grep -E "^[0-9]+:|inet " | head -10
}

# 显示配置摘要
show_config_summary() {
    log_info "配置文件摘要："
    if command -v grep >/dev/null 2>&1; then
        echo "Mixed Port: $(grep -E '^mixed-port:' /etc/mihomo/config.yaml | cut -d: -f2 | tr -d ' ' || echo 'Not configured')"
        echo "External Controller: $(grep -E '^external-controller:' /etc/mihomo/config.yaml | cut -d: -f2- | tr -d ' ' || echo 'Not configured')"
        echo "TUN Mode: $(grep -A5 '^tun:' /etc/mihomo/config.yaml | grep -E '^  enable:' | cut -d: -f2 | tr -d ' ' || echo 'Not configured')"
    fi
    echo "运行模式: $([ "$SUPERVISOR_ENABLED" = "true" ] && echo "Supervisor" || echo "Traditional")"
}

# 健康检查函数
healthcheck() {
    local exit_code=0

    # 检查mihomo服务
    if ! curl -f -s --connect-timeout 5 http://localhost:11024/version > /dev/null 2>&1; then
        log_error "mihomo服务健康检查失败"
        exit_code=1
    fi

    # 检查API服务
    if ! curl -f -s --connect-timeout 5 http://localhost:8888/health > /dev/null 2>&1; then
        log_error "API服务健康检查失败"
        exit_code=1
    fi

    if [ $exit_code -eq 0 ]; then
        log_success "所有服务健康检查通过"
    fi

    exit $exit_code
}

# 启动前检查
pre_start_checks() {
    log_info "开始启动前检查..."
    check_binary
    check_config
    show_version
    show_network_info
    show_config_summary
    log_success "启动前检查完成"
}

# Supervisor模式启动
start_with_supervisor() {
    log_info "使用 Supervisor 模式启动服务..."

    # 确保日志目录存在
    mkdir -p /var/log/supervisor /var/log/mihomo

    # 启动supervisor
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
}

# 启动 Python API 服务
start_api_server() {
    log_info "启动 Python API 服务器..."

    cd /app
    python main.py &
    API_PID=$!

    log_success "Python API 服务器已启动，PID: $API_PID"

    # 等待一下确保服务启动
    sleep 2
}

# 启动 mihomo
start_mihomo() {
    log_info "启动 mihomo..."

    # 设置日志级别
    export MIHOMO_LOG_LEVEL=${MIHOMO_LOG_LEVEL:-info}

    # 启动 mihomo 并获取 PID
    /usr/local/bin/mihomo -d /etc/mihomo &
    MIHOMO_PID=$!

    log_success "mihomo 已启动，PID: $MIHOMO_PID"
}

# 等待所有服务
wait_for_services() {
    log_info "等待服务运行..."

    # 等待任一进程退出
    while kill -0 "$MIHOMO_PID" 2>/dev/null && kill -0 "$API_PID" 2>/dev/null; do
        sleep 1
    done

    log_warning "检测到服务进程退出"

    # 清理其他进程
    if kill -0 "$MIHOMO_PID" 2>/dev/null; then
        log_info "停止 mihomo 进程..."
        kill -TERM "$MIHOMO_PID" 2>/dev/null || true
    fi

    if kill -0 "$API_PID" 2>/dev/null; then
        log_info "停止 API 服务器进程..."
        kill -TERM "$API_PID" 2>/dev/null || true
    fi
}

# 主函数
main() {
    log_info "=== mihomo Docker 容器启动 ==="
    log_info "时间: $(date)"
    log_info "容器ID: $(hostname)"

    case "$1" in
        "start")
            # 执行启动前检查
            pre_start_checks

            if [ "$SUPERVISOR_ENABLED" = "true" ]; then
                start_with_supervisor
            else
                # 传统模式启动
                start_api_server
                start_mihomo
                wait_for_services
            fi
            ;;
        "healthcheck")
            healthcheck
            ;;
        "mihomo")
            # 兼容旧版本
            pre_start_checks
            start_api_server
            start_mihomo
            wait_for_services
            ;;
        *)
            # 执行自定义命令
            log_info "执行自定义命令: $*"
            exec "$@"
            ;;
    esac
}

# 运行主函数
main "$@"
