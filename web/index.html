<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>mihomo 控制面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .config-section {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }

        .config-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }

        .status-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .status-label {
            color: #666;
            font-size: 0.9em;
        }

        .log-section {
            padding: 30px;
            background: #f8f9fa;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .hidden {
            display: none;
        }

        .flex {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .connection-status.connected {
            background: #51cf66;
            box-shadow: 0 0 8px rgba(81, 207, 102, 0.6);
        }

        .connection-status.disconnected {
            background: #ff6b6b;
            box-shadow: 0 0 8px rgba(255, 107, 107, 0.6);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .config-section {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 mihomo 控制面板</h1>
            <p>简单易用的代理服务管理界面</p>
        </div>

        <!-- 连接配置 -->
        <div class="config-section">
            <h2>🔧 连接配置</h2>
            <div class="flex">
                <div class="form-group" style="flex: 1;">
                    <label for="apiUrl">API 地址</label>
                    <input type="text" id="apiUrl" value="http://localhost:8888" placeholder="http://localhost:8888">
                </div>
                <div class="form-group" style="flex: 1;">
                    <label for="apiToken">API 令牌</label>
                    <input type="password" id="apiToken" value="jhxnb666" placeholder="输入API令牌">
                </div>
            </div>
            <div class="flex">
                <button class="btn" onclick="testConnection()">
                    <span class="connection-status disconnected" id="connectionStatus"></span>
                    测试连接
                </button>
                <button class="btn btn-success" onclick="getStatus()">获取状态</button>
                <button class="btn" onclick="getProxies()">获取代理</button>
                <button class="btn" onclick="getTraffic()">获取流量</button>
                <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="status-grid">
            <div class="status-card">
                <h3>📊 服务状态</h3>
                <div class="status-value" id="serviceStatus">未知</div>
                <div class="status-label">当前状态</div>
            </div>
            <div class="status-card">
                <h3>📈 上传流量</h3>
                <div class="status-value" id="uploadTraffic">0 B</div>
                <div class="status-label">总上传</div>
            </div>
            <div class="status-card">
                <h3>📉 下载流量</h3>
                <div class="status-value" id="downloadTraffic">0 B</div>
                <div class="status-label">总下载</div>
            </div>
            <div class="status-card">
                <h3>🔗 活跃连接</h3>
                <div class="status-value" id="activeConnections">0</div>
                <div class="status-label">当前连接数</div>
            </div>
        </div>

        <!-- 日志显示 -->
        <div class="log-section">
            <h2>📝 操作日志</h2>
            <div class="log-container" id="logContainer">
                欢迎使用 mihomo 控制面板！
                请先配置API地址和令牌，然后点击"测试连接"开始使用。
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isConnected = false;
        let statusInterval = null;

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTime() {
            return new Date().toLocaleTimeString();
        }

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = formatTime();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showAlert(message, type = 'info') {
            // 移除现有的alert
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // 创建新的alert
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            // 插入到配置区域后面
            const configSection = document.querySelector('.config-section');
            configSection.insertAdjacentElement('afterend', alert);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }

        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
        }

        // API调用函数
        async function makeApiCall(endpoint, options = {}) {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiToken = document.getElementById('apiToken').value.trim();

            if (!apiUrl || !apiToken) {
                throw new Error('请先配置API地址和令牌');
            }

            const url = `${apiUrl}${endpoint}`;
            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                },
            };

            const response = await fetch(url, { ...defaultOptions, ...options });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        }

        // 主要功能函数
        async function testConnection() {
            log('🔍 正在测试API连接...');
            
            try {
                const result = await makeApiCall('/health');
                updateConnectionStatus(true);
                log('✅ API连接成功！');
                showAlert('API连接成功！', 'success');
                
                // 连接成功后自动获取状态
                setTimeout(getStatus, 500);
            } catch (error) {
                updateConnectionStatus(false);
                log(`❌ API连接失败: ${error.message}`);
                showAlert(`连接失败: ${error.message}`, 'error');
            }
        }

        async function getStatus() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📊 正在获取服务状态...');
            
            try {
                const result = await makeApiCall('/api/mihomo/status');
                
                if (result.success) {
                    const data = result.data;
                    
                    // 更新状态显示
                    document.getElementById('serviceStatus').textContent = 
                        data.service_status === 'running' ? '🟢 运行中' : '🔴 已停止';
                    
                    if (data.traffic) {
                        document.getElementById('uploadTraffic').textContent = 
                            formatBytes(data.traffic.up || 0);
                        document.getElementById('downloadTraffic').textContent = 
                            formatBytes(data.traffic.down || 0);
                    }
                    
                    log(`✅ 状态获取成功 - 版本: ${data.version?.version || 'Unknown'}`);
                    showAlert('状态更新成功', 'success');
                } else {
                    throw new Error(result.message || '获取状态失败');
                }
            } catch (error) {
                log(`❌ 获取状态失败: ${error.message}`);
                showAlert(`获取状态失败: ${error.message}`, 'error');
            }
        }

        async function getProxies() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('🔗 正在获取代理信息...');
            
            try {
                const result = await makeApiCall('/api/mihomo/proxies');
                
                if (result.success) {
                    const proxies = result.data.proxies || {};
                    const proxyCount = Object.keys(proxies).length;
                    
                    log(`✅ 代理信息获取成功，共 ${proxyCount} 个代理节点`);
                    
                    // 显示部分代理信息
                    let proxyList = '';
                    let count = 0;
                    for (const [name, proxy] of Object.entries(proxies)) {
                        if (count < 5) { // 只显示前5个
                            proxyList += `  - ${name} (${proxy.type || 'Unknown'})\n`;
                            count++;
                        }
                    }
                    
                    if (proxyList) {
                        log(`代理节点列表:\n${proxyList}`);
                    }
                    
                    showAlert(`获取到 ${proxyCount} 个代理节点`, 'success');
                } else {
                    throw new Error(result.message || '获取代理信息失败');
                }
            } catch (error) {
                log(`❌ 获取代理信息失败: ${error.message}`);
                showAlert(`获取代理信息失败: ${error.message}`, 'error');
            }
        }

        async function getTraffic() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📈 正在获取流量统计...');
            
            try {
                const result = await makeApiCall('/api/mihomo/traffic');
                
                if (result.success) {
                    const traffic = result.data;
                    
                    // 更新流量显示
                    document.getElementById('uploadTraffic').textContent = 
                        formatBytes(traffic.up || 0);
                    document.getElementById('downloadTraffic').textContent = 
                        formatBytes(traffic.down || 0);
                    
                    log(`✅ 流量统计: 上传 ${formatBytes(traffic.up || 0)}, 下载 ${formatBytes(traffic.down || 0)}`);
                    showAlert('流量统计更新成功', 'success');
                } else {
                    throw new Error(result.message || '获取流量统计失败');
                }
            } catch (error) {
                log(`❌ 获取流量统计失败: ${error.message}`);
                showAlert(`获取流量统计失败: ${error.message}`, 'error');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').textContent = '';
            log('📝 日志已清空');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎉 mihomo 控制面板已加载完成');
            log('💡 提示: 请确保mihomo服务正在运行，并且API端口(8888)可访问');
            
            // 自动测试连接
            setTimeout(testConnection, 1000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        testConnection();
                        break;
                    case 'r':
                        e.preventDefault();
                        getStatus();
                        break;
                }
            }
        });
    </script>
</body>
</html>
