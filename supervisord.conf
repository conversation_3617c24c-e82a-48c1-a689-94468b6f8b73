[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
loglevel=info

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# mihomo 服务配置
[program:mihomo]
command=/usr/local/bin/mihomo -d /etc/mihomo
directory=/etc/mihomo
user=root
autostart=true
autorestart=true
startretries=3
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/mihomo/mihomo.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
environment=MIHOMO_LOG_LEVEL=%(ENV_MIHOMO_LOG_LEVEL)s

# Python API 服务配置
[program:api]
command=python main.py
directory=/app
user=root
autostart=true
autorestart=true
startretries=3
startsecs=5
redirect_stderr=true
stdout_logfile=/var/log/mihomo/api.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
environment=PYTHONPATH=/app,PYTHONUNBUFFERED=1,API_SECRET=%(ENV_API_SECRET)s,MIHOMO_SECRET=%(ENV_MIHOMO_SECRET)s

# 监控服务配置
[program:monitor]
command=/monitor.sh monitor
directory=/
user=root
autostart=true
autorestart=true
startretries=3
startsecs=5
redirect_stderr=true
stdout_logfile=/var/log/mihomo/monitor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=2
environment=MONITOR_INTERVAL=30,MAX_RESTART_ATTEMPTS=3

# 服务组配置
[group:zfmi]
programs=mihomo,api,monitor
priority=999
