#!/bin/sh

# 服务监控脚本
# 监控mihomo和API服务的健康状态，并在需要时重启服务

set -e

# 配置
MONITOR_INTERVAL=${MONITOR_INTERVAL:-30}
MAX_RESTART_ATTEMPTS=${MAX_RESTART_ATTEMPTS:-3}
RESTART_DELAY=${RESTART_DELAY:-10}
LOG_FILE="/var/log/mihomo/monitor.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_monitor() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 直接输出到stdout，supervisor会处理日志
    echo "[MONITOR] $timestamp [$level] $message"
}

# 检查服务状态
check_mihomo() {
    if curl -f -s --connect-timeout 5 --max-time 10 http://localhost:11024/version > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

check_api() {
    if curl -f -s --connect-timeout 5 --max-time 10 http://localhost:8888/health > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 重启服务
restart_service() {
    local service=$1
    local restart_count_file="/tmp/${service}_restart_count"
    local restart_count=0
    
    if [ -f "$restart_count_file" ]; then
        restart_count=$(cat "$restart_count_file")
    fi
    
    if [ $restart_count -ge $MAX_RESTART_ATTEMPTS ]; then
        log_monitor "ERROR" "服务 $service 重启次数已达上限 ($MAX_RESTART_ATTEMPTS)，停止重启尝试"
        return 1
    fi
    
    restart_count=$((restart_count + 1))
    echo $restart_count > "$restart_count_file"
    
    log_monitor "WARNING" "正在重启服务 $service (第 $restart_count 次尝试)"
    
    if command -v supervisorctl > /dev/null 2>&1; then
        # Supervisor模式
        supervisorctl restart "$service"
    else
        # 传统模式 - 这里需要更复杂的逻辑
        log_monitor "ERROR" "传统模式下的服务重启暂未实现"
        return 1
    fi
    
    sleep $RESTART_DELAY
    return 0
}

# 重置重启计数器
reset_restart_counter() {
    local service=$1
    local restart_count_file="/tmp/${service}_restart_count"
    
    if [ -f "$restart_count_file" ]; then
        rm -f "$restart_count_file"
        log_monitor "INFO" "重置服务 $service 的重启计数器"
    fi
}

# 主监控循环
monitor_services() {
    log_monitor "INFO" "开始监控服务，检查间隔: ${MONITOR_INTERVAL}秒"
    
    local mihomo_consecutive_failures=0
    local api_consecutive_failures=0
    local last_mihomo_status="unknown"
    local last_api_status="unknown"
    
    while true; do
        # 检查mihomo服务
        if check_mihomo; then
            if [ "$last_mihomo_status" != "healthy" ]; then
                log_monitor "SUCCESS" "mihomo服务恢复正常"
                reset_restart_counter "mihomo"
            fi
            last_mihomo_status="healthy"
            mihomo_consecutive_failures=0
        else
            mihomo_consecutive_failures=$((mihomo_consecutive_failures + 1))
            if [ "$last_mihomo_status" != "unhealthy" ]; then
                log_monitor "ERROR" "mihomo服务检查失败"
            fi
            last_mihomo_status="unhealthy"
            
            # 连续失败2次后尝试重启
            if [ $mihomo_consecutive_failures -ge 2 ]; then
                restart_service "mihomo"
                mihomo_consecutive_failures=0
            fi
        fi
        
        # 检查API服务
        if check_api; then
            if [ "$last_api_status" != "healthy" ]; then
                log_monitor "SUCCESS" "API服务恢复正常"
                reset_restart_counter "api"
            fi
            last_api_status="healthy"
            api_consecutive_failures=0
        else
            api_consecutive_failures=$((api_consecutive_failures + 1))
            if [ "$last_api_status" != "unhealthy" ]; then
                log_monitor "ERROR" "API服务检查失败"
            fi
            last_api_status="unhealthy"
            
            # 连续失败2次后尝试重启
            if [ $api_consecutive_failures -ge 2 ]; then
                restart_service "api"
                api_consecutive_failures=0
            fi
        fi
        
        sleep $MONITOR_INTERVAL
    done
}

# 信号处理
cleanup_monitor() {
    log_monitor "INFO" "监控服务收到终止信号，正在退出..."
    exit 0
}

trap cleanup_monitor SIGTERM SIGINT SIGQUIT

# 确保日志目录存在
mkdir -p "$(dirname "$LOG_FILE")"

# 启动监控
case "${1:-monitor}" in
    "monitor")
        monitor_services
        ;;
    "check")
        # 单次检查
        mihomo_ok=false
        api_ok=false
        
        if check_mihomo; then
            echo "mihomo: OK"
            mihomo_ok=true
        else
            echo "mihomo: FAIL"
        fi
        
        if check_api; then
            echo "API: OK"
            api_ok=true
        else
            echo "API: FAIL"
        fi
        
        if $mihomo_ok && $api_ok; then
            exit 0
        else
            exit 1
        fi
        ;;
    *)
        echo "Usage: $0 [monitor|check]"
        exit 1
        ;;
esac
