#!/bin/bash

# ZFMI Docker 部署脚本
# 用于简化Docker容器的构建、部署和管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="zfmi"
IMAGE_NAME="zfmi"
CONTAINER_NAME="zfmi"

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 初始化环境
init_env() {
    log_info "初始化环境..."
    
    # 创建 .env 文件
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        log_info "创建 .env 文件..."
        cp "$SCRIPT_DIR/.env.example" "$SCRIPT_DIR/.env"
        log_warning "请编辑 .env 文件以配置您的环境"
    fi
    
    # 创建数据目录
    DATA_DIR="${DATA_DIR:-./data}"
    mkdir -p "$DATA_DIR"/{backups,logs,supervisor}
    
    # 创建自定义配置目录
    CUSTOM_DIR="${CUSTOM_CONFIG_DIR:-./custom}"
    mkdir -p "$CUSTOM_DIR"
    
    log_success "环境初始化完成"
}

# 构建镜像
build_image() {
    log_info "构建 Docker 镜像..."
    
    local mihomo_version="${MIHOMO_VERSION:-v1.19.11}"
    
    docker build \
        --build-arg MIHOMO_VERSION="$mihomo_version" \
        --tag "$IMAGE_NAME:latest" \
        --tag "$IMAGE_NAME:$mihomo_version" \
        "$SCRIPT_DIR"
    
    log_success "镜像构建完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 加载环境变量
    if [ -f "$SCRIPT_DIR/.env" ]; then
        set -a
        source "$SCRIPT_DIR/.env"
        set +a
    fi
    
    docker-compose up -d
    
    log_success "服务启动完成"
    
    # 显示服务状态
    show_status
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    
    docker-compose down
    
    log_success "服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    
    stop_service
    start_service
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    
    docker-compose ps
    
    echo ""
    log_info "服务健康检查："
    
    # 等待服务启动
    sleep 5
    
    # 检查服务
    if curl -f -s --connect-timeout 5 http://localhost:11024/version > /dev/null 2>&1; then
        log_success "mihomo 服务正常"
    else
        log_error "mihomo 服务异常"
    fi
    
    if curl -f -s --connect-timeout 5 http://localhost:8888/health > /dev/null 2>&1; then
        log_success "API 服务正常"
    else
        log_error "API 服务异常"
    fi
}

# 查看日志
show_logs() {
    local service="${1:-}"
    
    if [ -n "$service" ]; then
        log_info "显示 $service 服务日志..."
        docker-compose logs -f "$service"
    else
        log_info "显示所有服务日志..."
        docker-compose logs -f
    fi
}

# 进入容器
enter_container() {
    log_info "进入容器..."
    
    docker exec -it "$CONTAINER_NAME" /bin/sh
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像（可选）
    read -p "是否删除镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rmi "$IMAGE_NAME:latest" 2>/dev/null || true
        log_success "镜像已删除"
    fi
    
    log_success "清理完成"
}

# 更新服务
update_service() {
    log_info "更新服务..."
    
    # 拉取最新代码（如果是git仓库）
    if [ -d "$SCRIPT_DIR/.git" ]; then
        log_info "拉取最新代码..."
        git pull
    fi
    
    # 重新构建镜像
    build_image
    
    # 重启服务
    restart_service
    
    log_success "服务更新完成"
}

# 备份配置
backup_config() {
    local backup_dir="$SCRIPT_DIR/backup/$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份配置到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    cp "$SCRIPT_DIR/config.yaml" "$backup_dir/" 2>/dev/null || true
    cp "$SCRIPT_DIR/.env" "$backup_dir/" 2>/dev/null || true
    
    # 备份数据目录
    if [ -d "$SCRIPT_DIR/data" ]; then
        cp -r "$SCRIPT_DIR/data" "$backup_dir/"
    fi
    
    log_success "配置备份完成"
}

# 显示帮助信息
show_help() {
    echo "ZFMI Docker 部署脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  init        初始化环境"
    echo "  build       构建 Docker 镜像"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      显示服务状态"
    echo "  logs [服务] 查看日志"
    echo "  shell       进入容器"
    echo "  update      更新服务"
    echo "  backup      备份配置"
    echo "  cleanup     清理资源"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 init     # 初始化环境"
    echo "  $0 build    # 构建镜像"
    echo "  $0 start    # 启动服务"
    echo "  $0 logs     # 查看所有日志"
    echo "  $0 logs zfmi # 查看zfmi服务日志"
}

# 主函数
main() {
    cd "$SCRIPT_DIR"
    
    case "${1:-help}" in
        "init")
            check_dependencies
            init_env
            ;;
        "build")
            check_dependencies
            build_image
            ;;
        "start")
            check_dependencies
            start_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            enter_container
            ;;
        "update")
            check_dependencies
            update_service
            ;;
        "backup")
            backup_config
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
